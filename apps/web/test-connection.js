#!/usr/bin/env node

// Test the updated database connection
const { PrismaClient } = require('./prisma/generated');

async function testConnection() {
  console.log('🔍 Testing updated database connection...');
  
  const prisma = new PrismaClient({
    log: ['query', 'info', 'warn', 'error'],
  });

  try {
    console.log('🔗 Connecting to database...');
    await prisma.$connect();
    console.log('✅ Connected successfully!');

    console.log('🧪 Testing basic query...');
    const result = await prisma.$queryRaw`SELECT 1 as test, current_database() as db_name, current_user as db_user`;
    console.log('✅ Query result:', result);

    console.log('📊 Testing subscription plans table...');
    const planCount = await prisma.subscriptionPlan.count();
    console.log(`✅ Found ${planCount} subscription plans`);

    console.log('👥 Testing users table...');
    const userCount = await prisma.user.count();
    console.log(`✅ Found ${userCount} users`);

    console.log('🎉 All tests passed! Database connection is working correctly.');

  } catch (error) {
    console.error('❌ Connection test failed:', {
      name: error.name,
      message: error.message,
      code: error.code,
    });
    
    if (error.message.includes('Can\'t reach database server')) {
      console.log('\n💡 Troubleshooting tips:');
      console.log('1. Check if Supabase project is paused');
      console.log('2. Verify DATABASE_URL is correct');
      console.log('3. Check network connectivity');
      console.log('4. Try using DIRECT_URL instead of pooler');
    }
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Disconnected from database');
  }
}

testConnection().catch(console.error);
